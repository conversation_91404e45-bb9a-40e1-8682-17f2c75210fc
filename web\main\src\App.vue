<template>
  <div class="app-container">
    <sidebar />
    <div class="app-content">
      <title-bar />
      <main class="main-content">
        <!-- 当显示登录弹窗时，背景显示骨架屏 -->
        <LibrarySkeleton v-if="isLogin === false" />
      </main>
    </div>
  </div>
</template>

<script>
import Sidebar from './components/Sidebar.vue';
import TitleBar from './components/TitleBar.vue';
import { LibrarySkeleton } from '@heybox-app-web-shared/components';
import { onMounted, ref, toRaw } from 'vue';
import { ipcService } from '@heybox-app-web-shared/ipc';
import { getUpdateUrl } from '@/api/update';
import { useEventBus } from '@heybox-app-web-shared/eventbus'
import { checkLoginStatus, compareVersions } from '@heybox-app-web-shared/utils';
import { getAccountInfo } from '../../popup/src/api/login';
import { useStore } from 'vuex'
import { useToast } from 'vue-toastification';
import AccManager from './assets/js/acc-manager';
import { handleLogout } from '@heybox-app-web-shared/utils';
import Protocol from '_lib/protocol'

export default {
  name: 'App',
  components: {
    Sidebar,
    TitleBar,
    LibrarySkeleton,
  },
  setup() {
    let _initedExeVersionCb = false

    const toast = useToast()
    const store = useStore()
    const { on: eventBusOn } = useEventBus();
    const isLogin = ref(false);
    const asar_version = ref('');
    const updateInfo = ref({});
    
    // TODO 还没有设置面板，先写死
    // 0: 不自动更新，1: 自动更新
    const init_auto_update_asar = ref(0);

    const initClientVersion = async () => {
      try {
        let version = await window.versionAPI.getClientVersion()
        asar_version.value = version.asar_version
        // this.SET_VERSION_INFO(version)

        const res = await getUpdateUrl({}, {})
        if (res.data.status === 'ok') {
          let d = res.data.result
          updateInfo.value = d
          await updateClientHandler(version)
        }
        console.log('[Main App] 版本检查完成');
      } catch (error) {
        console.error('[Main App] 版本检查过程中出错:', error);
        throw error;
      }
    }

    const updateClientHandler = async (clientVersion) => {
      let d = updateInfo.value
      if (d.has_new_version) {
        if (d.force_update || d.need_update_exe || !clientVersion.edit_authority) {
          store.commit('SET_HAS_NEW_VERSION', true)
          await window.popupManagerAPI.ensureInitialized();
          window.popupManagerAPI.show({
            type: 'Dialog',
            cptName: 'UpdateDialog',
            props: { dialogData: toRaw(updateInfo.value) }
          })
        } else if (d.asar_download_url) {
          let ignore_version = await window.electronAPI.getStoreData('ignore_version')
          if (ignore_version === d.latest_version) {
            return
          }
          if (init_auto_update_asar.value == 1) {
            updateAsar(d)
          } else {
            store.commit('SET_HAS_NEW_VERSION', true)
            await window.popupManagerAPI.ensureInitialized();
            window.popupManagerAPI.show({
              type: 'Dialog',
              cptName: 'UpdateDialog',
              props: { dialogData: toRaw(updateInfo.value), showTextBtn: true }
            })
          }
        }
      } else {
        let version_update_logs = await window.electronAPI.getStoreData('version_update_logs')
        if (version_update_logs === updateInfo.value.latest_version) {
            await window.popupManagerAPI.ensureInitialized();
            window.popupManagerAPI.show({
              type: 'Dialog',
              cptName: 'UpdateDialog',
              props: { dialogData: toRaw(updateInfo.value), onlyShowLogs: true }
            })
        }
      }
    }

    const updateAsar = (d, needProgress) => {
      window.versionAPI.updateAsarResource(d.latest_version, d.asar_download_url, (action, param) => {
        switch (action) {
          case 'success':
            if (needProgress) {
              ipcService.sendToTarget('popup', 'UpdateDialog:update_asar_progress', { type: 'success' })
              setTimeout(() => {
                const relaunchApp = true
                window.versionAPI.setAsarVersion(updateInfo.value.latest_version, relaunchApp)
              }, 200)
            } else {
              if (compareVersions(asar_version.value, updateInfo.value.tips_version) == -1) {
                window.electronAPI.setStoreData('version_update_logs', updateInfo.value.latest_version)
                updateInfo.value.asar_has_downloaded = true
                store.commit('SET_HAS_NEW_VERSION', true)
                store.commit('SET_NEW_VERSION_DOWNLOADED', true)
              }
              const relaunchApp = false
              window.versionAPI.setAsarVersion(updateInfo.value.latest_version, relaunchApp)
            }
            break;
          case 'error':
            if (needProgress) {
              updateInfo.value.asar_error = true
              ipcService.sendToTarget('popup', 'UpdateDialog:update_exe_start')
            } else {
              updateInfo.value.asar_error = true
              store.commit('SET_HAS_NEW_VERSION', true)
              window.popupManagerAPI.show({
                type: 'Dialog',
                cptName: 'UpdateDialog',
                props: {
                  dialogData: toRaw(updateInfo.value),
                }
              })
            }
            break;
          case 'progress': 
            if (needProgress) {
              ipcService.sendToTarget('popup', 'UpdateDialog:update_asar_progress', param)
            }
            break;
          case 'extract':
            if (needProgress) {
              ipcService.sendToTarget('popup', 'UpdateDialog:update_asar_progress', { type: 'extract' })
            }
          default:
            break;
        }
      })
    }

    const initExeVersionCb = () => {
      if(_initedExeVersionCb) return
      window.versionAPI.onUpdateExeProgress((_, value) => {
        ipcService.sendToTarget('popup', 'UpdateDialog:update_exe_progress', value)
      })
      window.versionAPI.onUpdateExeResult((_, value) => {
        if(!value) {
          toast.error('更新失败')
        }
        ipcService.sendToTarget('popup', 'UpdateDialog:update_exe_result', value)
      })
      _initedExeVersionCb = true
    }

    // 处理点击新版本提示
    const handleClickVersionTip = () => {
      if (updateInfo.value.asar_has_downloaded) {
        const config = {
          title: `新版本（${updateInfo.value.latest_version}）已准备就绪`,
          desc: '请重启黑盒语音以生效',
          disableClose: true,
          confirm: {
            text: '立即重启',
          },
          cancel: {
            text: '我知道了'
          },
        }
        const dialogId = 'NewVersionReLaunch'
        window.popupManagerAPI.show({
          type: 'Dialog',
          cptName: 'QueryDialog',
          props: {
            config,
            id: dialogId,
            target: 'main',
          }
        })

        // TODO 卸载
        ipcService.onMessage(`query-dialog-${dialogId}-response`, ({type, data}) => {
          switch(type) {
            case 'confirm': {
              console.log('queryConfirm')
              window.electronAPI.setStoreData('version_update_logs', updateInfo.value.latest_version)
              setTimeout(() => {
                const relaunchApp = true
                window.versionAPI.setAsarVersion(updateInfo.value.latest_version, relaunchApp)
              }, 50)
              break
            } 
            case 'cancel': {
              window.electronAPI.setStoreData('version_update_logs', updateInfo.value.latest_version)
              setTimeout(() => {
                updateInfo.value.asar_has_downloaded = true
                store.commit('SET_HAS_NEW_VERSION', true)
                store.commit('SET_NEW_VERSION_DOWNLOADED', true)
                const relaunchApp = false
                window.electronAPI.setAsarVersion(updateInfo.value.latest_version, relaunchApp)
              }, 50)
              break
            }
            case 'close': {
              // close的时候要将这个卸载掉
              break
            }
          }
        })
      } else {
        updateInfo.value.cancel = '我知道了'
        window.popupManagerAPI.show({
          type: 'Dialog',
          cptName: 'UpdateDialog',
          props: { dialogData: toRaw(updateInfo.value) }
        })
      }
    }

    onMounted(() => {
      ipcService.onMessage('accelerator:status', (status) => {
        console.log('accelerator:status', status);
      });

      eventBusOn('Click_Version_Tip', handleClickVersionTip)

      ipcService.onMessage('UpdateDialog:Start_Update_Asar', () => {
        updateAsar(updateInfo.value, true)
      })
      ipcService.onMessage('UpdateDialog:Init_Exe_Version_Cb', () => {
        initExeVersionCb()
      })
      ipcService.onMessage('initAcc', ({last_acc_setting, appid, acc_game_infos}) => {
        console.log('get init message')
        AccManager.handleAcc(last_acc_setting, appid, acc_game_infos)
      })
      ipcService.onMessage('handleLogout', () => {
        console.log('logout')
        handleLogout()
        showLoginDialog()
      })
      ipcService.onMessage('protocol', (url, info) => {
        Protocol.handleProtocol(url, info)
      })
      ipcService.onMessage('login-success', () => {
        isLogin.value = true
      })

      ipcService.onMessage('logout-acc', () => {
        console.log('logout-acc')
        AccManager.logoutAcc()
      });

      // 处理popup webview请求数美设备ID
      ipcService.onMessage('request-sm-device-id', async () => {

        try {
          await window._initSmPromise;

          const isInitialized = typeof window.dealSmDeviceId === 'function';
          let deviceId = null;

          if (isInitialized) {
            deviceId = await window.dealSmDeviceId();
          }

          const response = {
            success: isInitialized && !!deviceId,
            deviceId: deviceId,
            isInitialized: isInitialized,
            timestamp: Date.now()
          }

          if (!response.success && !response.error) {
            response.error = response.isInitialized ? '设备ID为空' : '数美SDK未初始化'
          }

          ipcService.sendToTarget('popup', 'sm-device-id-response', response)
        } catch (error) {
          console.error('[Main] 获取数美设备ID时发生错误:', error)
          const errorResponse = {
            success: false,
            error: '获取数美设备ID失败: ' + error.message,
            timestamp: Date.now()
          }
          ipcService.sendToTarget('popup', 'sm-device-id-response', errorResponse)
        }
      })
      eventBusOn('logout-complete', (data) => {
        console.log('收到登出完成消息，更新登录状态')
        isLogin.value = false
        showLoginDialog()
      })

      initClientVersion().then(() => {
        initLoginStatus();
      }).catch((error) => {
        console.error('[Main App] 版本检查失败:', error);
        initLoginStatus();
      });
    });

    const showLoginDialog = async () => {
      try {
        await window.popupManagerAPI.ensureInitialized();

        window.popupManagerAPI.hide({
          type: 'Dialog',
          cptName: 'UserSettingDialog'
        });

        window.popupManagerAPI.show({
          type: 'Dialog',
          cptName: 'LoginDialog',
          props: {}
        });
      } catch (error) {
        console.error('[Main App] 显示登录弹窗失败:', error);
      }
    }

    const initLoginStatus = async () => {
      try {
        const res = await checkLoginStatus(getAccountInfo)
        if (res) {
          console.log('[Main App] 登录状态检查成功，用户已登录');
          // window.webViewAPI?.loadDefaultWebView()
          const stored_account = window.electronAPI.getStoreData('', 'account')
          electronAPI.setStoreData('', {
            ...stored_account,
            ...res
          }, 'account')
          console.log('initLoginStatus');
          isLogin.value = true
        } else {
          isLogin.value = false
          electronAPI.setStoreData('', {}, 'account')
          showLoginDialog()
        }
      } catch (error) {
        console.error('[Main App] 登录状态检查失败:', error)
        isLogin.value = false
        showLoginDialog()
      }
    }

    return {
      isLogin,
    };
  },
};
</script>

<style lang="scss">
.app-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
  background-color: $general-color-bg-2;
}

.app-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-content {
  flex: 1;
  overflow: hidden;
  border-radius: 8px 8px 0 8px;
  background-color: #fff;
}
</style>
