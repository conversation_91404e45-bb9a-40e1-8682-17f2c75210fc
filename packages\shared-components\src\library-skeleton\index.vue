<template>
  <div class="skeleton-layout-main">
    <div class="skeleton-left-side" :style="{ width: leftSideWidth + 'px' }">
      <SkeletonSidebar />
    </div>
    <div class="skeleton-resizer"></div>
    <div class="skeleton-main-content">
      <div class="skeleton-content-wrapper">
        <SkeletonAccounts />
        <SkeletonGames />
      </div>
    </div>
  </div>
</template>

<script setup name="LibrarySkeleton">
import { ref } from 'vue';
import SkeletonSidebar from './components/SkeletonSidebar.vue';
import SkeletonAccounts from './components/SkeletonAccounts.vue';
import SkeletonGames from './components/SkeletonGames.vue';

const leftSideWidth = ref(280);
</script>

<style lang="scss">
.skeleton-layout-main {
  display: flex;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;

  .skeleton-left-side {
    overflow: hidden;
    height: 100%;
    flex-shrink: 0;
    background-color: #fff;
    border-right: 1px solid #e0e0e0;
  }

  .skeleton-resizer {
    width: 1px;
  }

  .skeleton-main-content {
    overflow: hidden;
    flex: 1;
    background-color: #fff;
    width: calc(100% + 8px);
    margin-right: -8px;

    .skeleton-content-wrapper {
      height: 100%;
      overflow-y: auto;
    }
  }
}
</style>
