const { app, ipcMain, WebContentsView, screen } = require('electron')
const path = require('path');
const { getWebUrl, updateWebContentsUa } = require('../assets/js/utils')
const { log } = require('@heybox/electron-utils')
const { DEFAULT_WEBVIEW } = require('../assets/constant/constant')
// TODO
const WEB_VIEW_URL = {
  chat: getWebUrl('chat'),
  library: getWebUrl('library'),
}
let MAIN_WINDOW_SIDEBAR_RECT = {
  width: global.main_config.sidebarWidth || 200,
  height: 36,
}
// TODO
const DESTROY_WEBVIEW_TIMEOUT = 1000 * 1

/**
 * WebContentsView管理器
 * 用于管理Electron应用中的WebView实例
 * 主要功能：
 * 1. 创建和管理WebView实例
 * 2. 处理WebView的显示和隐藏
 * 3. 管理WebView的生命周期
 * 4. 处理窗口大小变化
 * 5. 处理进程间通信
 */
class WebContentsManager {
  constructor() {
    // 存储WebView实例的Map
    this.webViewMap = new Map()
    // 存储WebView销毁定时器的Map
    this.destroyWebViewTimerMap = new Map()
    // 存储已销毁WebView的URL的Map
    this.destroyedWebViewUrlMap = new Map()
    // 当前活跃的WebView标识符
    this.currentActiveView = DEFAULT_WEBVIEW
    // 初始化IPC通信
    this.initIpc()
  }

  /**
   * 加载WebView
   * @param {string} key - WebView的标识符
   */
  async loadWebView(key) {
    if (!key) return
    if(key === 'default') {
      key = DEFAULT_WEBVIEW
    }
    if (key === 'library') {
      this.initSteamService()
    }
    if (Object.keys(WEB_VIEW_URL).includes(key)) {
      log.info(`[WebContentsManager] Loading WebView: ${key}`)
      log.info(`[WebContentsManager] this.webViewMap.has: ${this.webViewMap.has(key)}`)
      if (this.webViewMap.has(key)) {
        // 如果WebView已存在，检查是否已销毁
        const win = this.getWindow(key)
        log.info('win', win, '!win.webContents.isDestroyed()', !win.webContents.isDestroyed());
        
        if (win && !win.webContents.isDestroyed()) {
          win.setVisible(true)
          mainWindow.contentView.addChildView(win)
          log.info(`[WebContentsManager] Reused existing WebView: ${key}`)
        } else {
          // WebView已销毁，重新创建
          this.webViewMap.delete(key)
          const newWin = await this.createWebView(key, this.destroyedWebViewUrlMap.get(key))
          this.webViewMap.set(key, newWin)
          log.info(`[WebContentsManager] Recreated destroyed WebView: ${key}`)
        }
      } else {
        // 如果WebView不存在，则创建新的WebView
        await this.createWebView(key, this.destroyedWebViewUrlMap.get(key))
      }
      this.clearDestroyWebViewTimer(key)
      // 更新当前活跃视图
      this.currentActiveView = key
      log.info(`[WebContentsManager] Current active view updated to: ${key}`)
    } else {
      throw new Error(`不支持的key: ${key}`)
    }
  }

  /**
   * 预加载WebView
   * @param {string} key - WebView的标识符
   */
  async preloadWebView(key) {
    if (!key) return
    if (Object.keys(WEB_VIEW_URL).includes(key)) {
      if (!this.webViewMap.has(key)) {
        // 创建 WebView，真正显示需要 mainWindow.contentView.addChildView(win)
        const win = await this.createWebView(key, this.destroyedWebViewUrlMap.get(key))
        this.webViewMap.set(key, win)
        log.info(`[WebContentsManager] Preloaded and displayed WebView: ${key}`)
      }
    } else {
      throw new Error(`不支持的key: ${key}`)
    }
  }

  /**
   * 销毁WebView
   * @param {string} key - WebView的标识符
   */
  destroyWebView(key) {
    const win = this.getWindow(key)
    if (win) {
      // 保存URL以便重新创建时使用
      const url = win.webContents.getURL()
      this.destroyedWebViewUrlMap.set(key, url.replace('file:///', ''))
      mainWindow.contentView.removeChildView(win)
      win.webContents.close()
      this.webViewMap.delete(key)
    }
  }

  destroyAllWebView() {
    this.webViewMap.keys().forEach(key => {
      this.destroyWebView(key)
    })
  }

  /**
   * 隐藏WebView
   * @param {string} [key] - WebView的标识符，如果不传则隐藏所有WebView
   */
  hideWebView(key) {
    if (key) {
      const win = this.getWindow(key)
      if (win) {
        win.setVisible(false)
        this.setDestroyWebViewTimer(key)
      }
    } else {
      this.webViewMap.forEach((win, key) => {
        win.setVisible(false)
        this.setDestroyWebViewTimer(key)
      })
    }
  }

  /**
   * 设置WebView销毁定时器
   * @param {string} key - WebView的标识符
   */
  setDestroyWebViewTimer(key) {
    this.clearDestroyWebViewTimer(key)
    this.destroyWebViewTimerMap.set(key, setTimeout(() => {
      this.destroyWebView(key)
      setTimeout(() => {
        sendMemeryReport('destroy_webview')
      }, 1000)
    }, DESTROY_WEBVIEW_TIMEOUT))
  }

  /**
   * 清除WebView销毁定时器
   * @param {string} key - WebView的标识符
   */
  clearDestroyWebViewTimer(key) {
    const timer = this.destroyWebViewTimerMap.get(key)
    if (timer) {
      clearTimeout(timer)
      this.destroyWebViewTimerMap.delete(key)
    }
  }
  
  /**
   * 获取WebView实例
   * @param {string} key - WebView的标识符
   * @returns {WebContentsView} WebView实例
   */
  getWindow(key) {
    return this.webViewMap.get(key)
  }

  /**
   * 创建新的WebView实例
   * @param {string} key - WebView的标识符
   * @param {string} [url] - 加载的URL
   * @returns {Promise<WebContentsView>} WebView实例
   */
  async createWebView(key, url) {
    const view = new WebContentsView({
      show: true,
      webPreferences: {
        preload: path.join(__dirname, `../preload/${key}.js`),
        transparent: true,
        nodeIntegration: true,
        additionalArguments: [`--window-id=${key}`]
      },
    })
    const mainWindowBounds = mainWindow.getBounds()
    view.setBounds({
      ...this.getWebViewBounds(mainWindowBounds),
      preload: path.join(__dirname, `../preload/${key}.js`),
    })
    mainWindow.contentView.addChildView(view)
    this.webViewMap.set(key, view)
    updateWebContentsUa(view.webContents)
    if (IS_LOCAL_DEV) {
      await view.webContents.loadURL(url || WEB_VIEW_URL[key])
    } else {
      // TODO 通过router定位当前页面刷新前的页面
      await view.webContents.loadFile(WEB_VIEW_URL[key])
    }

    // 添加F12开发者工具快捷键支持
    view.webContents.on('before-input-event', (event, input) => {
      if (input.code == 'F12' && (main_config.devTool || !app.isPackaged)) {
        try {
          view.webContents.openDevTools({
            mode: 'detach',
            activate: true,
          });
          console.log('Opening DevTools...');
        } catch (error) {
          console.error('Failed to open DevTools:', error);
        }
      }
    })
    return view
  }

  /**
   * 初始化IPC通信
   */
  initIpc() {
    // 处理WebView加载请求
    ipcMain.on('webView:load', async (_, ...arg) => {
      try {
        this.hideWebView()
        await this.loadWebView(...arg)
        setTimeout(() => {
          sendMemeryReport('load_webview')
        }, 1000)
        
      } catch (error) {
        log.error('webView:load', error)
      }
    })

    ipcMain.handle('webView:getDefaultWebView', () => {
      return DEFAULT_WEBVIEW
    })

    // 处理WebView销毁请求
    ipcMain.on('webView:destroy', (_, ...arg) => {
      this.destroyWebView(...arg)
    })

    // 处理WebView销毁请求
    ipcMain.on('webView:destroyAll', (_) => {
      this.destroyAllWebView()
    })
    
    // 处理WebView消息传递
    ipcMain.on('webView:postMessage', (_, key, ...arg) => {
      if (key === 'main') {
        // 发送消息到主窗口
        mainWindow?.webContents.send('webView:receiveMessage', ...arg)
      } else if (key === 'all') {
        // 发送消息到所有窗口
        mainWindow?.webContents.send('webView:receiveMessage', ...arg)
        this.webViewMap.forEach(win => {
          win.webContents.send('webView:receiveMessage', ...arg)
        });
      } else {
        // 发送消息到指定窗口
        const win = this.getWindow(key)
        if (!win) return
        win.webContents.send('webView:receiveMessage', ...arg)
      }
    })
    ipcMain.on('webView:resizeSidebar', (_, {width, height}) => {
      if (width) {
        MAIN_WINDOW_SIDEBAR_RECT.width = width
      }
      if (height) {
        MAIN_WINDOW_SIDEBAR_RECT.width = height
      }
      this.webViewMap.forEach(view => {
        view.setBounds(this.getWebViewBounds(mainWindow.getBounds()))
      })
    })
  }

  /**
   * 初始化窗口大小变化监听
   */
  initResizeWatch() {
    global.mainWindow.on('resize', () => {
      this.webViewMap.forEach(view => {
        view.setBounds(this.getWebViewBounds(mainWindow.getBounds()))
      })
    })
  }

  /**
   * 计算WebView的边界位置和大小
   * @param {Object} mainWindowBounds - 主窗口的边界信息
   * @returns {Object} WebView的边界信息
   */
  getWebViewBounds(mainWindowBounds) {
    // 检查窗口是否全屏
    const isFullScreen = mainWindow.isMaximized()
    let effectiveBounds = mainWindowBounds
    
    // 如果窗口全屏，使用屏幕的实际可用区域
    if (isFullScreen) {
      const primaryDisplay = screen.getPrimaryDisplay()
      const workArea = primaryDisplay.workArea
      effectiveBounds = {
        x: workArea.x,
        y: workArea.y,
        width: workArea.width,
        height: workArea.height,
      }
    }
    
    return {
      x: MAIN_WINDOW_SIDEBAR_RECT.width,
      y: MAIN_WINDOW_SIDEBAR_RECT.height,
      width: effectiveBounds.width - MAIN_WINDOW_SIDEBAR_RECT.width,
      height: effectiveBounds.height - MAIN_WINDOW_SIDEBAR_RECT.height,
    }
  }

  /**
   * 为 library webview 初始化 Steam 服务
   */
  initSteamService() {
    try {
      // 检查是否已经有全局实例
      if (!global.steamService) {
        const SteamService = require('../process/steam_service');
        global.steamService = new SteamService();
        console.log('[Main] Steam service initialized successfully');
      } else {
        console.log('[Main] Steam service already initialized');
      }
    } catch (error) {
      console.error('[Main] Failed to initialize Steam service:', error);
    }
  }
}


module.exports = new WebContentsManager()
