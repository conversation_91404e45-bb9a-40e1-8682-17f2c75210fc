<template>
  <Dialog
    class="cpt-steam-install-dialog"
    v-model="show"
    :config="dialogConfig"
    :disableClose="true"
    :disableWrapperClose="true"
    @confirm="confirmHandler"
    @cancel="cancelHandler"
    @close="close"
  ></Dialog>
</template>

<script setup name="SteamInstallDialog">
import { ref } from 'vue';
import {
  useSubComponent,
  getDefaultSubProps,
} from '../composables/useSubComponent';

const props = defineProps(getDefaultSubProps());

const { show, handleClose, Dialog } = useSubComponent({
  initDialog: ({ config: configData }) => {
    if (configData) {
      Object.assign(dialogConfig.value, configData);
    }
    show.value = true;
  },
});

const dialogConfig = ref({
  title: '',
  desc: '使用该功能请先安装Steam',
  confirm: {
    text: '前往官网',
    type: 'primary'
  },
  cancel: {
    text: '取消',
    type: 'cancel'
  },
});

const close = () => {
  handleClose();
};

const confirmHandler = async () => {
  // 打开 Steam 官网
  try {
    // 使用 electronAPI 调用主进程的 shell.openExternal
    await window.electronAPI.invoke('shell:openExternal', 'https://store.steampowered.com/about/');
  } catch (error) {
    console.error('Failed to open Steam website:', error);
  }
  close();
};

const cancelHandler = () => {
  close();
};
</script>

<style lang="scss">
.cpt-steam-install-dialog {
  .hb-dialog-container {
    width: 315px;
    height: 143px;
    padding: 24px 20px 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: #f5f5f5;
    border-radius: 8px;
  }

  .dialog-header-group {
    text-align: center;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin: 0 0 8px 0;
    }

    .desc {
      font-size: 14px;
      color: #666;
      line-height: 1.4;
      margin: 0;
    }
  }

  .bottom-wrapper {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 0;

    .cpt-chat-form-button {
      width: 80px;
      height: 32px;
      border-radius: 4px;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;

      &.cancel {
        background: #d9d9d9;
        color: #666;

        &:hover {
          background: #bfbfbf;
        }
      }

      &.primary {
        background: #1976d2;
        color: white;

        &:hover {
          background: #1565c0;
        }

        // Steam 图标样式
        &::before {
          content: '';
          display: inline-block;
          width: 16px;
          height: 16px;
          margin-right: 4px;
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9.93 15v1c0 1.1.9 2 2 2v-.07zM17.9 17.39c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z'/%3E%3C/svg%3E");
          background-size: contain;
          background-repeat: no-repeat;
        }
      }
    }
  }
}
</style>
