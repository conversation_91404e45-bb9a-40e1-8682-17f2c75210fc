const { contextBridge, ipc<PERSON>enderer } = require('electron');
const store = require('@heybox/electron-utils/store')
const { version } = require('../../package.json');

contextBridge.exposeInMainWorld('electronAPI', {
  // 发送消息到主进程
  send: (channel, ...args) => {
    ipcRenderer.send(channel, ...args);
  },

  // 从主进程接收消息
  receive: (channel, callback) => {
    // 删除任何现有监听器
    ipcRenderer.removeAllListeners(channel);

    // 添加新的监听器
    ipcRenderer.on(channel, (event, ...args) => callback(...args));
  },

  // 调用主进程方法并等待结果
  invoke: async (channel, ...args) => {
    return await ipcRenderer.invoke(channel, ...args);
  },

  // Steam 下载状态监听器
  onSteamDownloadStatus: (callback) => {
    ipcRenderer.on('steam:download-status-update', (event, downloadStatus) => {
      callback(downloadStatus);
    });
  },

  sentryReport: (message, stack) => ipcRenderer.send('sentryReport', message, stack),
  setCacheData: (k, v) => {
    ipcRenderer.send('levelstore:set', k, JSON.parse(v))
  },
  delCacheData: (k) => ipcRenderer.invoke('levelstore:del', k),
  getCacheData: (k) => ipcRenderer.invoke('levelstore:get', k),
  getStoreData: (k, base = 'library_config') => {
    return store.get(`${base}${k ? `.${k}` : ''}`)
  },
  setStoreData: (k, v, base = 'library_config') => {
    store.set(`${base}${k ? `.${k}` : ''}`, v)
  },
  getStoreCookies: () => ipcRenderer.invoke('getStoreCookies'),
  delStoreData: (k, base = 'library_config') => {
    try {
      if(!k) throw new Error('k is required')
      return store.del(`${base}.${k}`)
    } catch (error) {
      console.error('delStoreData error', error)
    }
  },
  onStoreChange: (k, cb, base = 'library_config') => {
    return store.original_store.onDidChange(`${base}${k ? `.${k}` : ''}`, (v) => {
      cb(v)
    })
  },
  getRegeditKey: (path, key) => ipcRenderer.invoke('getRegeditKey', path, key),
  getFileData: (path) => ipcRenderer.invoke('getFileData', path),
  writeFileData: (path, data) => ipcRenderer.invoke('writeFileData', path, data),
  getDeviceId: () => ipcRenderer.invoke('getDeviceId'),
  switchSteamAccount: (accountName) => ipcRenderer.invoke('switchSteamAccount', accountName),
  setOfflineMode: (accountName) => ipcRenderer.invoke('setOfflineMode', accountName),
  openMiniProgram: (moduleName, options) => ipcRenderer.invoke('openMiniProgram', moduleName, options),
  base: 'library_config'
});

contextBridge.exposeInMainWorld('webViewAPI', {
  load: (viewId) => ipcRenderer.send('webView:load', viewId),
  destroy: (viewId) => ipcRenderer.send('webView:destroy', viewId),
  postMessage: (...args) => ipcRenderer.send('webView:postMessage', ...args),
  onMessage: (event, callback) => {
    ipcRenderer.on('webView:receiveMessage', (_, receivedEvent, ...args) => {
      if (event === receivedEvent) {
        callback(...args);
      }
    });
  },
})

contextBridge.exposeInMainWorld('popupManagerAPI', {
  show: (options) => ipcRenderer.send('popup:show', options),
  hide: (options) => ipcRenderer.send('popup:hide', options),
  onMessage: (event, callback) => {
    ipcRenderer.on('popup:receiveMessage', (receivedEvent, ...args) => {
      if (event === receivedEvent) {
        callback(...args);
      }
    });
  },
});

contextBridge.exposeInMainWorld('accAPI', {
  initAcc: (smDeviceId) => ipcRenderer.send('acc-sdk:init-acc', smDeviceId),
  startGame: (launch_schema) => ipcRenderer.invoke('acc-sdk:start-game', launch_schema),
  onEventListener: (events) => {
    Object.keys(events).forEach(key => {
      ipcRenderer.on(key, (e, ...args) => {
        events[key](...args)
      })
    })
  },

  // sdk
  startAcc: (params) => ipcRenderer.invoke('heybox:sdk-start-accelerate', params),
  stopAcc: (params) => ipcRenderer.invoke('heybox:sdk-stop-accelerate', params),
  sendRequest: (url, method, params, form) => ipcRenderer.invoke('heybox:send-request', url, method, params, form),
  loginAcc: (params) => ipcRenderer.invoke('heybox:login-by-qr', params),
});

contextBridge.exposeInMainWorld('steamGames', {
  // Steam 服务管理
  startService: () => ipcRenderer.invoke('steam:start-service'),
  stopService: () => ipcRenderer.invoke('steam:stop-service'),

  // 游戏状态检查
  checkGameStatus: (appId) => ipcRenderer.invoke('steam:check-game-status', appId),

  // 游戏操作
  installGame: (appId, folderIdx) => ipcRenderer.invoke('steam:install-game', appId, folderIdx),
  runGame: (appId) => ipcRenderer.invoke('steam:run-game', appId),
  terminateGame: (appId) => ipcRenderer.invoke('steam:terminate-game', appId),

  // 游戏信息
  getAppInfo: (appId) => ipcRenderer.invoke('steam:get-app-info', appId),
  getAppsInfo: () => ipcRenderer.invoke('steam:get-apps-info'),
  getDownloadOverview: () => ipcRenderer.invoke('steam:get-download-overview'),

  // 快捷方式管理
  addShortcut: (options) => ipcRenderer.invoke('steam:add-shortcut', options),
  removeShortcut: (appId) => ipcRenderer.invoke('steam:remove-shortcut', appId),
  createDesktopShortcut: (appId) => ipcRenderer.invoke('steam:create-desktop-shortcut', appId),
});

contextBridge.exposeInMainWorld('steamAPI', {
  getSteamApps: (account_name) => ipcRenderer.invoke('steam:get-steam-apps', account_name),
});

contextBridge.exposeInMainWorld('versionAPI', {
  version,
});